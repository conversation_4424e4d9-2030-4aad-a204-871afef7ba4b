import { authApi } from "@/context/auth-context";

/**
 * Service for inventory management operations.
 * Used by admin and employee roles to manage company inventory.
 */
const inventoryService = {
  /**
   * Get all inventory products with pagination and search.
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction (asc/desc)
   * @param {string} params.search - Search term (optional)
   * @returns {Promise<Object>} Response with paginated inventory products
   */
  getInventoryProducts: async (params = {}) => {
    try {
      const {
        page = 0,
        size = 12,
        sortBy = "label",
        sortDir = "asc",
        search,
      } = params;

      const response = await authApi.get("/api/v1/company-products", {
        params: {
          page,
          size,
          sortBy,
          sortDir,
          ...(search && { query: search }),
        },
      });

      // Validate response structure
      if (!response.data || !response.data.data) {
        console.error("Invalid response structure:", response.data);
        return {
          success: false,
          message: "Invalid response from server",
          data: { content: [], totalElements: 0, totalPages: 0 },
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching inventory products:", error);

      // Return a structured error response instead of throwing
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to load inventory products",
        data: { content: [], totalElements: 0, totalPages: 0 },
      };
    }
  },

  /**
   * Get inventory statistics.
   * @returns {Promise<Object>} Response with inventory statistics
   */
  getInventoryStatistics: async () => {
    try {
      const response = await authApi.get("/api/v1/company-products/stats");

      if (!response.data || !response.data.data) {
        console.error("Invalid response structure:", response.data);
        return {
          success: false,
          message: "Invalid response from server",
          data: {},
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching inventory statistics:", error);
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Failed to load inventory statistics",
        data: {},
      };
    }
  },

  /**
   * Update the selling price of an inventory product.
   * @param {string} productId - The company product ID
   * @param {number} sellingPrice - The new selling price
   * @returns {Promise<Object>} Response with updated product
   */
  updateProductPrice: async (productId, sellingPrice) => {
    try {
      const response = await authApi.put(
        `/api/v1/company-products/${productId}/price`,
        {
          sellingPrice: parseFloat(sellingPrice),
        }
      );

      return response.data;
    } catch (error) {
      console.error("Error updating product price:", error);

      let errorMessage = "Failed to update product price";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = "Invalid price value provided";
      } else if (error.response?.status === 404) {
        errorMessage = "Product not found in inventory";
      }

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  },

  /**
   * Update a company product with full details.
   * @param {string} productId - The company product ID
   * @param {Object} productData - The product data to update
   * @param {string} productData.label - The product label
   * @param {string} productData.description - The product description
   * @param {number} productData.sellingPrice - The selling price
   * @param {number} productData.averageDeliveryTime - The delivery time
   * @param {string} productData.imageUrl - The product image URL
   * @returns {Promise<Object>} Response with updated product
   */
  updateProduct: async (productId, productData) => {
    try {
      const response = await authApi.put(
        `/api/v1/company-products/${productId}`,
        {
          label: productData.label,
          description: productData.description,
          sellingPrice: parseFloat(productData.sellingPrice),
          averageDeliveryTime: parseInt(productData.averageDeliveryTime),
          imageUrl: productData.imageUrl,
        }
      );

      return response.data;
    } catch (error) {
      console.error("Error updating product:", error);

      let errorMessage = "Failed to update product";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = "Invalid product data provided";
      } else if (error.response?.status === 404) {
        errorMessage = "Product not found in inventory";
      } else if (error.response?.status === 409) {
        errorMessage = "A product with this name already exists";
      }

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  },

  /**
   * Upload a product image.
   * @param {File} imageFile - The image file to upload
   * @returns {Promise<Object>} Response with image URL
   */
  uploadProductImage: async (imageFile) => {
    try {
      const formData = new FormData();
      formData.append("image", imageFile);

      const response = await authApi.post("/api/v1/upload/image", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error uploading image:", error);

      let errorMessage = "Failed to upload image";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 413) {
        errorMessage = "Image file is too large";
      } else if (error.response?.status === 415) {
        errorMessage = "Invalid image format";
      }

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  },

  /**
   * Delete a product from company inventory.
   * @param {string} productId - The company product ID
   * @returns {Promise<Object>} Response indicating success or failure
   */
  deleteInventoryProduct: async (productId) => {
    try {
      const response = await authApi.delete(
        `/api/v1/company-products/${productId}`
      );

      return response.data;
    } catch (error) {
      console.error("Error deleting inventory product:", error);

      let errorMessage = "Failed to delete product from inventory";

      if (error.response?.data?.message) {
        const serverMessage = error.response.data.message;

        // Handle specific constraint violation messages
        if (
          serverMessage.includes("database relationship constraints") ||
          serverMessage.includes("constraint")
        ) {
          errorMessage =
            "Cannot delete this product due to database constraints. This product may have active orders or historical data.";
        } else {
          errorMessage = serverMessage;
        }
      } else if (error.response?.status === 404) {
        errorMessage = "Product not found in inventory";
      } else if (error.response?.status === 409) {
        // Conflict status usually indicates constraint violations
        errorMessage =
          "Cannot delete this product due to database constraints. This product may have active orders or historical data.";
      }

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  },

  /**
   * Get a single inventory product by ID.
   * @param {string} productId - The company product ID
   * @returns {Promise<Object>} Response with product details
   */
  getInventoryProduct: async (productId) => {
    try {
      const response = await authApi.get(
        `/api/v1/company-products/${productId}`
      );

      return response.data;
    } catch (error) {
      console.error("Error fetching inventory product:", error);
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to load product details",
        data: null,
      };
    }
  },
};

export default inventoryService;
