import { servicesData } from "@/data";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  FingerprintIcon,
} from "lucide-react";
import { createElement, useEffect, useRef, useState } from "react";

export const Services = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollContainerRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  // Determine if we're on mobile/tablet or desktop
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const scrollNext = () => {
    if (currentIndex < servicesData.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          left:
            (currentIndex + 1) *
            (scrollContainerRef.current.scrollWidth / servicesData.length),
          behavior: "smooth",
        });
      }
    }
  };

  const scrollPrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          left:
            (currentIndex - 1) *
            (scrollContainerRef.current.scrollWidth / servicesData.length),
          behavior: "smooth",
        });
      }
    }
  };

  return (
    <section
      id="services"
      className="font-light relative flex flex-col gap-y-12 px-4 py-12 lg:p-12 min-h-dvh bg-blue-gray-50/60"
    >
      <div className="rounded-2xl overflow-hidden border border-blue-gray-200 shadow-gray-500/20 shadow-md">
        <div className="flex flex-col lg:flex-row items-center justify-between p-6 lg:p-12 border-b border-blue-gray-200">
          <p className="capitalize text-header text-center text-blue-gray-800 font-semibold mb-4 lg:mb-0">
            What Do We Offer ?
          </p>
          <div className="inline-flex size-12 lg:size-16 items-center justify-center rounded-full bg-blue-gray-900 text-center shadow-gray-500/20 shadow-md">
            <FingerprintIcon className="size-6 lg:size-8 text-crimson" />
          </div>
        </div>
        <p className="font-normal text-lg lg:text-2xl text-blue-gray-700 text-center p-6 lg:p-12">
          The <span className="text-crimson">S</span>tore
          <span className="text-crimson">F</span>low platform provides seamless
          access to essential shopping services, including real-time order
          tracking, secure purchases, and tailored product recommendations,
          ensuring a smooth and efficient experience.
        </p>
      </div>
      {isMobile ? (
        <div className="overflow-hidden relative border rounded-2xl border-blue-gray-200 shadow-gray-500/20 shadow-md">
          {/* Pagination controls */}
          <button
            onClick={scrollPrev}
            disabled={currentIndex === 0}
            className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 z-10 ${
              currentIndex === 0
                ? "text-slate-200"
                : "text-blue-gray-800 hover:text-crimson cursor-pointer"
            } transition-colors`}
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="size-8" />
          </button>

          <button
            onClick={scrollNext}
            disabled={currentIndex >= servicesData.length - 1}
            className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-1 z-10 ${
              currentIndex >= servicesData.length - 1
                ? "text-slate-200"
                : "text-blue-gray-800 hover:text-crimson cursor-pointer"
            } transition-colors`}
            aria-label="Next slide"
          >
            <ChevronRightIcon className="size-8" />
          </button>

          {/* Scrollable container */}
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto scrollbar-hide snap-x snap-mandatory flex gap-6 lg:gap-8 px-4 py-2"
          >
            {servicesData.map(({ title, icon, description }, index) => (
              <div
                key={index}
                className="p-6 flex flex-col gap-y-3 items-center snap-center flex-shrink-0 w-full"
              >
                <span className="bg-blue-gray-900 grid place-items-center size-10 lg:size-14 rounded-full">
                  {createElement(icon, {
                    className: "size-4 text-crimson",
                  })}
                </span>
                <p className="font-semibold text-xl lg:text-2xl text-center text-blue-gray-800">
                  {title}
                </p>
                <p className="text-base lg:text-xl text-center font-normal text-blue-gray-700">
                  {description}
                </p>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-12">
          {servicesData.map(({ title, icon, description }, index) => (
            <div
              key={index}
              className="p-12 flex flex-col gap-y-3 items-center shadow-gray-500/20 shadow-md rounded-2xl border border-blue-gray-200"
            >
              <span className="bg-blue-gray-900 grid place-items-center size-14 rounded-full">
                {createElement(icon, { className: "size-6 text-crimson" })}
              </span>
              <p className="font-semibold text-2xl text-center text-blue-gray-800">
                {title}
              </p>
              <p className="text-xl text-center font-normal text-blue-gray-700">
                {description}
              </p>
            </div>
          ))}
        </div>
      )}
    </section>
  );
};

export default Services;
