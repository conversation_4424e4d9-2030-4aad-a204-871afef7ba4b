import { RadioIcon, TargetIcon } from "lucide-react";

export const About = () => {
  return (
    <section
      id="about"
      className="relative grid place-items-center px-4 py-12 lg:px-12 min-h-dvh text-blue-gray-700 bg-blue-gray-50/40 font-normal"
    >
      <div className="grid lg:grid-cols-2 lg:gap-12 border border-blue-gray-200 rounded-2xl overflow-hidden shadow-gray-500/20 shadow-md">
        <div className="flex flex-col">
          <div className="flex items-center justify-between p-6 lg:p-12 border-b lg:-mr-12 border-blue-gray-200">
            <p className="capitalize font-satoshi font-semibold text-header text-blue-gray-800">
              About Us
            </p>
            <div className="inline-flex size-12 lg:size-16 items-center justify-center rounded-full bg-blue-gray-900 text-center shadow-md">
              <RadioIcon className="size-6 lg:size-8 text-crimson" />
            </div>
          </div>
          <p className="text-lg lg:text-xl p-6 lg:py-12 lg:pl-12 lg:pr-0">
            <span className="text-crimson">S</span>tore
            <span className="text-crimson">F</span>low offers an intuitive
            platform for simplify the daily management of stocks, orders and
            sales. <br />
            <br />
            Users can consult in real time the product information, track order
            status, and access a personalized dashboard to view sales and
            available stocks. <br />
            <br />
            The platform also allows employees to manage their work schedules
            and receive notifications for urgent tasks, thus ensuring management
            smooth and efficient awning operations.
          </p>
        </div>
        <div className="h-full shadow-md border-t lg:border-t-0 lg:border-l border-blue-gray-200 shadow-gray-500/20 overflow-hidden py-6 lg:py-12">
          <div className="relative h-56 px-6 lg:px-12">
            <img
              alt="Store Image"
              src="/img/overview.jpeg"
              className="h-full w-full object-cover rounded-2xl"
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-3 border-b border-blue-gray-200 mx-6 lg:mx-0 lg:px-12 py-3">
              <p className="font-semibold text-2xl text-blue-gray-800">
                Overview
              </p>
              <div className="inline-flex size-10 items-center justify-center rounded-full bg-blue-gray-900 text-center shadow-md">
                <TargetIcon className="size-5 text-crimson" />
              </div>
            </div>
            <p className="text-lg px-6 lg:px-12">
              <span className="text-crimson">S</span>tore
              <span className="text-crimson">F</span>low enhances operational
              efficiency by providing real-time insights and automated
              notifications. It ensures better coordination between
              administrators and employees, facilitates task prioritization, and
              improves workflow management, making daily operations more
              seamless and responsive.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
