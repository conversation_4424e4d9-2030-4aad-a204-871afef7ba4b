import { Profile } from "@/components/portals";
import {
  Dashboard,
  EmployeePurchases,
  EmployeeOrders,
} from "@/components/portals/employee";
import { PurchaseProducts } from "@/components/shared";
import InventoryManagement from "@/components/inventory/inventory-management";
import {
  CircleUserRoundIcon,
  HouseIcon,
  ShoppingCartIcon,
  ClipboardListIcon,
  ListOrderedIcon,
  PackageIcon,
} from "lucide-react";

const icon = {
  className: "size-[25px] text-inherit",
};

export const routes = [
  {
    icon: <HouseIcon {...icon} />,
    name: "dashboard",
    path: "/dashboard",
    element: <Dashboard />,
  },
  {
    icon: <CircleUserRoundIcon {...icon} />,
    name: "profile",
    path: "/profile",
    element: <Profile />,
  },
  {
    icon: <PackageIcon {...icon} />,
    name: "inventory",
    path: "/inventory",
    element: <InventoryManagement />,
  },
  {
    icon: <ShoppingCartIcon {...icon} />,
    name: "purchase",
    path: "/purchase",
    element: <PurchaseProducts />,
  },
  {
    icon: <ClipboardListIcon {...icon} />,
    name: "purchases",
    path: "/purchases",
    element: <EmployeePurchases />,
  },
  {
    icon: <ListOrderedIcon {...icon} />,
    name: "orders",
    path: "/orders",
    element: <EmployeeOrders />,
  },
];

export default routes;
