import { authApi } from "@/context/auth-context";

/**
 * Service for catalog-related API calls
 */
export const catalogService = {
  /**
   * Get all available products in the catalog with pagination
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getAvailableProducts: async (params = {}) => {
    try {
      const { page = 0, size = 12, sortBy = "label", sortDir = "asc" } = params;

      const response = await authApi.get("/api/v1/catalog/products", {
        params: {
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      // Validate response structure
      if (!response.data || !response.data.data) {
        console.error("Invalid response structure:", response.data);
        return {
          success: false,
          message: "Invalid response from server",
          data: { content: [], totalElements: 0, totalPages: 0 },
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching catalog products:", error);

      // Return a structured error response instead of throwing
      return {
        success: false,
        message: error.response?.data?.message || "Failed to load products",
        data: { content: [], totalElements: 0, totalPages: 0 },
      };
    }
  },

  /**
   * Get a specific product from the catalog by label
   * @param {string} label - The product label
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getProductByLabel: async (label) => {
    try {
      const response = await authApi.get(
        `/api/v1/catalog/products/${encodeURIComponent(label)}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching catalog product:", error);
      throw error;
    }
  },
};
