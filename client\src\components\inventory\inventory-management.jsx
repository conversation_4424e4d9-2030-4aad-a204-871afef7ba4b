import { useState, useEffect } from "react";
import {
  Package,
  Search,
  Edit,
  Trash,
  Eye,
  MoreVertical,
  Loader2,
  AlertTriangle,
  DollarSign,
  Clock,
  TrendingUp,
  Image as ImageIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Pagination } from "@/components/ui/pagination";
import { useFormik } from "formik";
import * as Yup from "yup";
import { inventoryService } from "@/services";
import { toast } from "sonner";

const InventoryManagement = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1); // UI uses 1-based pagination
  const [totalPages, setTotalPages] = useState(1);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [loadingAction, setLoadingAction] = useState(false);
  const itemsPerPage = 6; // Match supplier portal

  // Format currency like supplier portal
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("fr-MA", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: true,
    }).format(amount);
  };

  // Fetch products from backend
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await inventoryService.getInventoryProducts({
        page: currentPage - 1, // Convert to 0-based for API
        size: itemsPerPage,
        sortBy: "label",
        sortDir: "asc",
        search: searchQuery.trim() || undefined,
      });

      if (response.success) {
        const { content, totalPages: pages } = response.data;
        setProducts(content || []);
        setTotalPages(pages || 1);
      } else {
        toast.error("Failed to fetch inventory products");
        setProducts([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch inventory products");
      setProducts([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Load products when component mounts or when search/pagination changes
  useEffect(() => {
    fetchProducts();
  }, [currentPage, searchQuery]);

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle view product details
  const handleViewProductDetails = (product) => {
    setSelectedProduct(product);
    setIsViewDialogOpen(true);
  };

  // Handle edit product
  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    editFormik.setValues({
      sellingPrice: product.sellingPrice,
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete product
  const handleDeleteProduct = (product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete product
  const confirmDeleteProduct = async () => {
    if (selectedProduct) {
      try {
        setLoadingAction(true);
        const response = await inventoryService.deleteInventoryProduct(
          selectedProduct.id
        );

        if (response.success) {
          toast.success("Product deleted successfully");
          setIsDeleteDialogOpen(false);
          fetchProducts();
        } else {
          toast.error(response.message || "Failed to delete product");
        }
      } catch (error) {
        console.error("Error deleting product:", error);
        toast.error("Failed to delete product");
      } finally {
        setLoadingAction(false);
      }
    }
  };

  // Product validation schema
  const ProductSchema = Yup.object().shape({
    sellingPrice: Yup.number()
      .min(0.01, "Price must be greater than 0")
      .required("Selling price is required"),
  });

  // Edit product form
  const editFormik = useFormik({
    initialValues: {
      sellingPrice: "",
    },
    validationSchema: ProductSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        setLoadingAction(true);

        const response = await inventoryService.updateProductPrice(
          selectedProduct.id,
          parseFloat(values.sellingPrice)
        );

        if (response.success) {
          toast.success("Product updated successfully");
          setIsEditDialogOpen(false);
          resetForm();
          fetchProducts();
        } else {
          toast.error(response.message || "Failed to update product");
        }
      } catch (error) {
        console.error("Error updating product:", error);
        toast.error("Failed to update product");
      } finally {
        setLoadingAction(false);
      }
    },
  });

  // Get current page products
  const getCurrentPageProducts = () => {
    if (!Array.isArray(products)) return [];
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return products.slice(startIndex, endIndex);
  };

  return (
    <div className="space-y-6">
      <div className="flex items">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-blue-gray-900">
              Inventory Management
            </h1>
            <p className="text-blue-gray-600">
              Manage your company's product inventory
            </p>
          </div>
        </div>

        {/* Search */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center w-full  border border-blue-gray-500 rounded-xl focus-within:border-blue-gray-800 transition-all h-[48px] sm:h-[53px]">
            <Search className="ml-3 size-4 sm:size-5 lg:size-6 text-blue-gray-500 flex-shrink-0" />
            <input
              type="text"
              className="flex-1 h-full text-sm sm:text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
              placeholder="Search products..."
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          // Loading state
          Array(itemsPerPage)
            .fill(0)
            .map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="h-48 bg-blue-gray-100 rounded-t-xl"></div>
                <div className="p-5 space-y-3">
                  <div className="h-4 bg-blue-gray-100 rounded w-3/4"></div>
                  <div className="h-3 bg-blue-gray-100 rounded w-1/2"></div>
                  <div className="h-8 bg-blue-gray-100 rounded"></div>
                </div>
              </Card>
            ))
        ) : getCurrentPageProducts().length > 0 ? (
          // Products list
          getCurrentPageProducts().map((product) => (
            <Card
              key={product.id}
              className="overflow-hidden border border-blue-gray-100 rounded-xl hover:shadow-md transition-all duration-300 group hover-lift"
            >
              <div className="relative">
                {/* Stock Indicator Badge */}
                <div className="absolute top-3 left-3 z-10">
                  <Badge
                    className={`px-2 py-1 text-xs font-medium bg-white border border-blue-gray-100 text-blue-gray-700 shadow-sm`}
                  >
                    {product.stockQuantity <= 0
                      ? "Out of Stock"
                      : product.stockQuantity < 5
                      ? "Low Stock"
                      : `${product.stockQuantity} in stock`}
                  </Badge>
                </div>

                {/* Product Image - Large header image with white background */}
                <div className="w-full h-48 bg-white flex items-center justify-center overflow-hidden border-b border-blue-gray-50">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.label}
                      className="h-full w-full object-contain p-4"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center">
                      <ImageIcon className="h-16 w-16 text-blue-gray-200" />
                      <p className="text-xs text-blue-gray-400 mt-2">
                        No image available
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-5">
                {/* Product Header */}
                <div className="mb-3">
                  <h3 className="text-lg font-bold text-blue-gray-800 group-hover:text-blue-gray-700 transition-colors truncate">
                    {product.label}
                  </h3>
                </div>

                {/* Product Description - Truncated */}
                <p className="text-sm text-blue-gray-600 line-clamp-2 mb-4 h-10">
                  {product.description}
                </p>

                {/* Stats Row */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-4 w-4 text-blue-gray-500" />
                    <span className="text-xs font-medium text-blue-gray-700">
                      {product.averageDeliveryTime}{" "}
                      {product.averageDeliveryTime === 1 ? "day" : "days"}{" "}
                      delivery
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5 bg-blue-gray-50 text-blue-gray-700 rounded-full py-1 px-2.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs font-semibold">
                      {product.margin?.toFixed(1) || 0}% margin
                    </span>
                  </div>
                </div>

                {/* Price Row - Redesigned with prices stacked vertically */}
                <div className="relative border-t border-blue-gray-50 pt-4">
                  {/* Prices in a vertical layout for better space management */}
                  <div className="flex flex-col gap-3 pr-12">
                    <div>
                      <div className="text-xs text-blue-gray-500">Purchase</div>
                      <div className="flex items-baseline">
                        <div className="font-medium text-blue-gray-800 tabular-nums">
                          {formatCurrency(product.averageCostPrice || 0)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1.5">
                          MAD
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-blue-gray-500">Selling</div>
                      <div className="flex items-baseline">
                        <div className="font-semibold text-blue-gray-800 tabular-nums">
                          {formatCurrency(product.sellingPrice)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1.5">
                          MAD
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions Dropdown - Positioned absolutely to avoid layout issues */}
                  <div className="absolute right-0 top-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full hover:bg-blue-gray-50 cursor-pointer"
                        >
                          <MoreVertical className="h-4 w-4 text-blue-gray-700" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="border-blue-gray-100"
                      >
                        <DropdownMenuItem
                          onClick={() => handleViewProductDetails(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Eye className="h-4 w-4 mr-2" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleEditProduct(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit Product
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-blue-gray-100" />
                        <DropdownMenuItem
                          onClick={() => handleDeleteProduct(product)}
                          className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete Product
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          // Empty state
          <div className="col-span-1 md:col-span-2 lg:col-span-3 py-12 text-center">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-blue-gray-100 max-w-md mx-auto">
              <div className="bg-blue-gray-50/60 rounded-full p-4 w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Package className="h-10 w-10 text-blue-gray-700" />
              </div>
              <h3 className="text-xl font-medium text-blue-gray-800 mb-2">
                {searchQuery ? "No matching products" : "No products found"}
              </h3>
              <p className="text-blue-gray-600 mb-6">
                {searchQuery
                  ? "No products match your search criteria. Try a different search term or clear the search."
                  : "No products in inventory yet."}
              </p>
              {searchQuery && (
                <Button
                  variant="outline"
                  className="mr-2 border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
                  onClick={() => setSearchQuery("")}
                >
                  Clear Search
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && products.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-100 p-4 mt-6">
          <div className="flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.max(1, totalPages)}
              onPageChange={handlePageChange}
            />
          </div>

          <div className="text-center text-blue-gray-500 mt-4 text-sm">
            Showing{" "}
            {Math.min(products.length, (currentPage - 1) * itemsPerPage + 1)} to{" "}
            {Math.min(products.length, currentPage * itemsPerPage)} of{" "}
            {products.length} products
          </div>
        </div>
      )}

      {/* View Product Details Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-gray-800">
              <Eye className="h-5 w-5 text-blue-gray-700 mr-2" />
              Product Details
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              View detailed information about this product.
            </DialogDescription>
          </DialogHeader>
          {selectedProduct && (
            <div className="grid gap-4 py-4">
              {/* Product Image */}
              <div className="w-full h-48 bg-blue-gray-50 rounded-lg flex items-center justify-center">
                {selectedProduct.imageUrl ? (
                  <img
                    src={selectedProduct.imageUrl}
                    alt={selectedProduct.label}
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <ImageIcon className="h-16 w-16 text-blue-gray-200" />
                    <p className="text-xs text-blue-gray-400 mt-2">
                      No image available
                    </p>
                  </div>
                )}
              </div>

              {/* Product Details */}
              <div className="grid gap-3">
                <div>
                  <Label className="text-blue-gray-600">Product Name</Label>
                  <p className="text-blue-gray-900 font-medium">
                    {selectedProduct.label}
                  </p>
                </div>
                <div>
                  <Label className="text-blue-gray-600">Description</Label>
                  <p className="text-blue-gray-900">
                    {selectedProduct.description}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-blue-gray-600">Stock Quantity</Label>
                    <p className="text-blue-gray-900 font-medium">
                      {selectedProduct.stockQuantity}
                    </p>
                  </div>
                  <div>
                    <Label className="text-blue-gray-600">Delivery Time</Label>
                    <p className="text-blue-gray-900 font-medium">
                      {selectedProduct.averageDeliveryTime} days
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-blue-gray-600">Purchase Price</Label>
                    <p className="text-blue-gray-900 font-medium">
                      {formatCurrency(selectedProduct.averageCostPrice || 0)}{" "}
                      MAD
                    </p>
                  </div>
                  <div>
                    <Label className="text-blue-gray-600">Selling Price</Label>
                    <p className="text-blue-gray-900 font-medium">
                      {formatCurrency(selectedProduct.sellingPrice)} MAD
                    </p>
                  </div>
                </div>
                <div>
                  <Label className="text-blue-gray-600">Margin</Label>
                  <p className="text-green-600 font-semibold">
                    {selectedProduct.margin?.toFixed(1) || 0}%
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Product Modal */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-blue-gray-900">
              Edit Product
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={editFormik.handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="sellingPrice" className="flex items-center">
                  Selling Price (MAD)
                  <span className="text-red-500 -ml-1">*</span>
                </Label>
                <Input
                  id="sellingPrice"
                  name="sellingPrice"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={editFormik.values.sellingPrice}
                  onChange={editFormik.handleChange}
                  onBlur={editFormik.handleBlur}
                  className={
                    editFormik.touched.sellingPrice &&
                    editFormik.errors.sellingPrice
                      ? "border-red-300"
                      : ""
                  }
                />
                {editFormik.touched.sellingPrice &&
                  editFormik.errors.sellingPrice && (
                    <p className="text-sm text-red-500">
                      {editFormik.errors.sellingPrice}
                    </p>
                  )}
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={loadingAction}
                className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loadingAction || !editFormik.isValid}
                className="bg-blue-gray-800 hover:bg-blue-gray-700 text-white"
              >
                {loadingAction ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Updating...
                  </>
                ) : (
                  "Update Product"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-gray-800">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              Delete Product
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              This action cannot be undone. This will permanently delete the
              product from your inventory.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-blue-gray-700">
              Are you sure you want to delete{" "}
              <span className="font-semibold">"{selectedProduct?.label}"</span>?
            </p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={loadingAction}
              className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDeleteProduct}
              disabled={loadingAction}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {loadingAction ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                "Delete Product"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryManagement;
