import { Profile, Suppliers } from "@/components/portals";
import {
  Dashboard,
  Employees,
  AdminPurchases,
} from "@/components/portals/admin";
import { PurchaseProducts } from "@/components/shared";
import InventoryManagement from "@/components/inventory/inventory-management";
import {
  CircleUserRoundIcon,
  CreditCardIcon,
  HouseIcon,
  ShoppingBagIcon,
  UsersRoundIcon,
  ShoppingCartIcon,
  ClipboardListIcon,
  PackageIcon,
} from "lucide-react";

const icon = {
  className: "size-[25px] text-inherit",
};

export const routes = [
  {
    icon: <HouseIcon {...icon} />,
    name: "dashboard",
    path: "/dashboard",
    element: <Dashboard />,
  },
  {
    icon: <CircleUserRoundIcon {...icon} />,
    name: "profile",
    path: "/profile",
    element: <Profile />,
  },
  {
    icon: <UsersRoundIcon {...icon} />,
    name: "employees",
    path: "/employees",
    element: <Employees />,
  },
  {
    icon: <UsersRoundIcon {...icon} />,
    name: "suppliers",
    path: "/suppliers",
    element: <Suppliers />,
  },
  {
    icon: <PackageIcon {...icon} />,
    name: "inventory",
    path: "/inventory",
    element: <InventoryManagement />,
  },
  {
    icon: <ShoppingCartIcon {...icon} />,
    name: "purchase",
    path: "/purchase",
    element: <PurchaseProducts />,
  },
  {
    icon: <ClipboardListIcon {...icon} />,
    name: "purchases",
    path: "/purchases",
    element: <AdminPurchases />,
  },
];

export default routes;
